"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppearanceProvider: function() { return /* binding */ AppearanceProvider; },\n/* harmony export */   useAppearance: function() { return /* binding */ useAppearance; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useAppearance,AppearanceProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Configurações padrão\nconst DEFAULT_SETTINGS = {\n    fonte: \"Inter\",\n    tamanhoFonte: 14,\n    palavrasPorSessao: 5000,\n    sessionsEnabled: true\n};\n// Criar contexto\nconst AppearanceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Hook para usar o contexto\nconst useAppearance = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppearanceContext);\n    if (!context) {\n        throw new Error(\"useAppearance must be used within an AppearanceProvider\");\n    }\n    return context;\n};\n_s(useAppearance, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Função para obter username do Firestore\nconst getUsernameFromFirestore = async (userEmail)=>{\n    try {\n        // Buscar documento pelo campo email (não pelo ID do documento)\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n        const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n        const q = query(usuariosRef, where(\"email\", \"==\", userEmail));\n        const querySnapshot = await getDocs(q);\n        if (!querySnapshot.empty) {\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            return userData.username || userEmail.split(\"@\")[0];\n        }\n        return userEmail.split(\"@\")[0]; // fallback\n    } catch (error) {\n        console.error(\"Erro ao obter username:\", error);\n        return userEmail.split(\"@\")[0]; // fallback\n    }\n};\n// Provider do contexto\nconst AppearanceProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_SETTINGS);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Carregar configurações do Firestore\n    const loadSettings = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            setSettings(DEFAULT_SETTINGS);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const username = await getUsernameFromFirestore(user.email);\n            const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n            if (configDoc.exists()) {\n                const config = configDoc.data();\n                if (config.aparencia) {\n                    setSettings({\n                        fonte: config.aparencia.fonte || DEFAULT_SETTINGS.fonte,\n                        tamanhoFonte: config.aparencia.tamanhoFonte || DEFAULT_SETTINGS.tamanhoFonte,\n                        palavrasPorSessao: config.aparencia.palavrasPorSessao || DEFAULT_SETTINGS.palavrasPorSessao,\n                        sessionsEnabled: config.aparencia.sessionsEnabled !== undefined ? config.aparencia.sessionsEnabled : DEFAULT_SETTINGS.sessionsEnabled\n                    });\n                } else {\n                    setSettings(DEFAULT_SETTINGS);\n                }\n            } else {\n                setSettings(DEFAULT_SETTINGS);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            setSettings(DEFAULT_SETTINGS);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Carregar configurações quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        user\n    ]);\n    // Aplicar configurações às variáveis CSS globais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            const root = document.documentElement;\n            root.style.setProperty(\"--chat-font-family\", settings.fonte);\n            root.style.setProperty(\"--chat-font-size\", \"\".concat(settings.tamanhoFonte, \"px\"));\n            root.style.setProperty(\"--chat-words-per-session\", settings.palavrasPorSessao.toString());\n            root.style.setProperty(\"--chat-sessions-enabled\", settings.sessionsEnabled ? \"1\" : \"0\");\n        }\n    }, [\n        settings,\n        isLoading\n    ]);\n    // Função para atualizar configurações\n    const updateSettings = async (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        setSettings(updatedSettings);\n        // Salvar no Firestore automaticamente\n        if (user) {\n            try {\n                const username = await getUsernameFromFirestore(user.email);\n                const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n                // Buscar configurações existentes\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n                const existingConfig = configDoc.exists() ? configDoc.data() : {};\n                // Atualizar apenas a seção de aparência\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(configRef, {\n                    ...existingConfig,\n                    aparencia: updatedSettings,\n                    updatedAt: new Date().toISOString()\n                }, {\n                    merge: true\n                });\n                console.log(\"✅ Configura\\xe7\\xf5es de apar\\xeancia salvas automaticamente\");\n            } catch (error) {\n                console.error(\"❌ Erro ao salvar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            }\n        }\n    };\n    // Função para aplicar configurações a um elemento específico\n    const applyToElement = (element)=>{\n        element.style.fontFamily = settings.fonte;\n        element.style.fontSize = \"\".concat(settings.tamanhoFonte, \"px\");\n    };\n    // Função para obter variáveis CSS como objeto\n    const getCSSVariables = ()=>{\n        return {\n            \"--chat-font-family\": settings.fonte,\n            \"--chat-font-size\": \"\".concat(settings.tamanhoFonte, \"px\"),\n            \"--chat-words-per-session\": settings.palavrasPorSessao.toString()\n        };\n    };\n    const value = {\n        settings,\n        updateSettings,\n        isLoading,\n        applyToElement,\n        getCSSVariables\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppearanceContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\contexts\\\\AppearanceContext.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AppearanceProvider, \"GTB8At3kv228hKHqctGDrslQ74E=\", false, function() {\n    return [\n        _AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = AppearanceProvider;\nvar _c;\n$RefreshReg$(_c, \"AppearanceProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AppearanceContext.tsx\n"));

/***/ })

});